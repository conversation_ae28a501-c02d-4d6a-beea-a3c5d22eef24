/**
 * Custom String Compression Module
 * 
 * This module provides custom string compression functionality to replace js-confuser's
 * built-in stringCompression option. It uses Babel to parse and transform the AST,
 * compresses strings using LZ-String, and generates completely obfuscated variable names.
 */

const babel = require('@babel/core');
const t = require('@babel/types');
const LZString = require('lz-string');
const { generateRandomName } = require('./utils');

/**
 * Generates a completely random and non-descriptive variable name
 * @returns {string} Random variable name
 */
function generateObfuscatedName() {
    // Generate names that give no indication of their purpose
    const prefixes = ['_', '$', '__', '$$'];
    const prefix = prefixes[Math.floor(Math.random() * prefixes.length)];
    return prefix + generateRandomName(Math.floor(Math.random() * 8) + 4);
}

/**
 * Creates the LZ-String decompression library with obfuscated names
 * @returns {Object} Object containing library code and method name
 */
function createObfuscatedLZStringLibrary() {
    const decompressMethodName = generateObfuscatedName();
    const vars = Array.from({length: 20}, () => generateObfuscatedName());

    // Simplified but functional LZ-String decompression
    const libraryCode = `(function() {
        var ${vars[0]} = String.fromCharCode;

        function ${decompressMethodName}(${vars[1]}) {
            if (${vars[1]} == null) return "";
            if (${vars[1]} == "") return null;
            return ${vars[2]}(${vars[1]}.length, 16384, function(${vars[3]}) {
                return ${vars[1]}.charCodeAt(${vars[3]}) - 32;
            });
        }

        function ${vars[2]}(${vars[4]}, ${vars[5]}, ${vars[6]}) {
            var ${vars[7]}, ${vars[8]}, ${vars[9]}, ${vars[10]}, ${vars[11]}, ${vars[12]}, ${vars[13]},
                ${vars[14]} = [], ${vars[15]} = 4, ${vars[16]} = 4, ${vars[17]} = 3, ${vars[18]} = "",
                ${vars[19]} = [], ${vars[1]} = 0, ${vars[3]} = 0;

            for (${vars[1]} = 0; ${vars[1]} < 3; ${vars[1]} += 1) ${vars[14]}[${vars[1]}] = ${vars[1]};

            ${vars[1]} = 0;
            ${vars[11]} = Math.pow(2, 2);
            ${vars[12]} = 1;

            for (${vars[10]} = {val: ${vars[6]}(0), position: ${vars[5]}, index: 1}; ${vars[12]} != ${vars[11]};) {
                ${vars[13]} = ${vars[10]}.val & ${vars[10]}.position;
                ${vars[10]}.position >>= 1;
                if (${vars[10]}.position == 0) {
                    ${vars[10]}.position = ${vars[5]};
                    ${vars[10]}.val = ${vars[6]}(${vars[10]}.index++);
                }
                ${vars[1]} |= (${vars[13]} > 0 ? 1 : 0) * ${vars[12]};
                ${vars[12]} <<= 1;
            }

            switch (${vars[1]}) {
                case 0:
                    ${vars[1]} = 0;
                    ${vars[11]} = Math.pow(2, 8);
                    ${vars[12]} = 1;
                    for (; ${vars[12]} != ${vars[11]};) {
                        ${vars[13]} = ${vars[10]}.val & ${vars[10]}.position;
                        ${vars[10]}.position >>= 1;
                        if (${vars[10]}.position == 0) {
                            ${vars[10]}.position = ${vars[5]};
                            ${vars[10]}.val = ${vars[6]}(${vars[10]}.index++);
                        }
                        ${vars[1]} |= (${vars[13]} > 0 ? 1 : 0) * ${vars[12]};
                        ${vars[12]} <<= 1;
                    }
                    ${vars[18]} = ${vars[0]}(${vars[1]});
                    break;
                case 1:
                    ${vars[1]} = 0;
                    ${vars[11]} = Math.pow(2, 16);
                    ${vars[12]} = 1;
                    for (; ${vars[12]} != ${vars[11]};) {
                        ${vars[13]} = ${vars[10]}.val & ${vars[10]}.position;
                        ${vars[10]}.position >>= 1;
                        if (${vars[10]}.position == 0) {
                            ${vars[10]}.position = ${vars[5]};
                            ${vars[10]}.val = ${vars[6]}(${vars[10]}.index++);
                        }
                        ${vars[1]} |= (${vars[13]} > 0 ? 1 : 0) * ${vars[12]};
                        ${vars[12]} <<= 1;
                    }
                    ${vars[18]} = ${vars[0]}(${vars[1]});
                    break;
                case 2:
                    return "";
            }

            ${vars[14]}[3] = ${vars[18]};
            ${vars[8]} = ${vars[18]};
            ${vars[19]}.push(${vars[18]});

            while (true) {
                if (${vars[10]}.index > ${vars[4]}) return "";

                ${vars[1]} = 0;
                ${vars[11]} = Math.pow(2, ${vars[17]});
                ${vars[12]} = 1;
                for (; ${vars[12]} != ${vars[11]};) {
                    ${vars[13]} = ${vars[10]}.val & ${vars[10]}.position;
                    ${vars[10]}.position >>= 1;
                    if (${vars[10]}.position == 0) {
                        ${vars[10]}.position = ${vars[5]};
                        ${vars[10]}.val = ${vars[6]}(${vars[10]}.index++);
                    }
                    ${vars[1]} |= (${vars[13]} > 0 ? 1 : 0) * ${vars[12]};
                    ${vars[12]} <<= 1;
                }

                switch (${vars[18]} = ${vars[1]}) {
                    case 0:
                        ${vars[1]} = 0;
                        ${vars[11]} = Math.pow(2, 8);
                        ${vars[12]} = 1;
                        for (; ${vars[12]} != ${vars[11]};) {
                            ${vars[13]} = ${vars[10]}.val & ${vars[10]}.position;
                            ${vars[10]}.position >>= 1;
                            if (${vars[10]}.position == 0) {
                                ${vars[10]}.position = ${vars[5]};
                                ${vars[10]}.val = ${vars[6]}(${vars[10]}.index++);
                            }
                            ${vars[1]} |= (${vars[13]} > 0 ? 1 : 0) * ${vars[12]};
                            ${vars[12]} <<= 1;
                        }
                        ${vars[14]}[${vars[16]}++] = ${vars[0]}(${vars[1]});
                        ${vars[18]} = ${vars[16]} - 1;
                        ${vars[15]}--;
                        break;
                    case 1:
                        ${vars[1]} = 0;
                        ${vars[11]} = Math.pow(2, 16);
                        ${vars[12]} = 1;
                        for (; ${vars[12]} != ${vars[11]};) {
                            ${vars[13]} = ${vars[10]}.val & ${vars[10]}.position;
                            ${vars[10]}.position >>= 1;
                            if (${vars[10]}.position == 0) {
                                ${vars[10]}.position = ${vars[5]};
                                ${vars[10]}.val = ${vars[6]}(${vars[10]}.index++);
                            }
                            ${vars[1]} |= (${vars[13]} > 0 ? 1 : 0) * ${vars[12]};
                            ${vars[12]} <<= 1;
                        }
                        ${vars[14]}[${vars[16]}++] = ${vars[0]}(${vars[1]});
                        ${vars[18]} = ${vars[16]} - 1;
                        ${vars[15]}--;
                        break;
                    case 2:
                        return ${vars[19]}.join("");
                }

                if (${vars[15]} == 0) {
                    ${vars[15]} = Math.pow(2, ${vars[17]});
                    ${vars[17]}++;
                }

                if (${vars[14]}[${vars[18]}]) {
                    ${vars[9]} = ${vars[14]}[${vars[18]}];
                } else {
                    if (${vars[18]} === ${vars[16]}) {
                        ${vars[9]} = ${vars[8]} + ${vars[8]}.charAt(0);
                    } else {
                        return null;
                    }
                }

                ${vars[19]}.push(${vars[9]});
                ${vars[14]}[${vars[16]}++] = ${vars[8]} + ${vars[9]}.charAt(0);
                ${vars[8]} = ${vars[9]};

                if (--${vars[15]} == 0) {
                    ${vars[15]} = Math.pow(2, ${vars[17]});
                    ${vars[17]}++;
                }
            }
        }

        return {
            ${decompressMethodName}: ${decompressMethodName}
        };
    })()`;

    return {
        code: libraryCode,
        methodName: decompressMethodName
    };
}

/**
 * Process code with custom string compression
 * @param {string} code - The JavaScript code to process
 * @returns {Object} Result object with processed code and metadata
 */
async function processStringCompression(code) {
    try {
        const startTime = Date.now();
        
        // Collection of strings to compress
        const stringMap = new Map();
        let stringIndex = 0;
        
        // Generate obfuscated names
        const decoderFunctionName = generateObfuscatedName();
        const stringArrayName = generateObfuscatedName();
        const libName = generateObfuscatedName();
        const stringDelimiter = String.fromCharCode(Math.floor(Math.random() * 26) + 65); // Random delimiter
        
        // Parse the code with Babel
        const ast = babel.parseSync(code, {
            sourceType: 'script',
            parserOpts: {
                allowImportExportEverywhere: true,
                allowReturnOutsideFunction: true
            }
        });
        
        // Traverse the AST to find and collect strings
        babel.traverse(ast, {
            StringLiteral(path) {
                const stringValue = path.node.value;

                // Skip very short strings or strings that might break functionality
                if (stringValue.length < 3 ||
                    stringValue.includes('use strict') ||
                    stringValue.includes('require') ||
                    stringValue.includes('module') ||
                    stringValue.includes('exports')) {
                    return;
                }

                // Skip if this string literal is part of an object property key
                // that might have been transformed by previous processes
                if (path.isObjectProperty && path.isObjectProperty()) {
                    return;
                }

                // Skip if parent is a computed member expression key
                if (t.isMemberExpression(path.parent) && path.parent.computed && path.parent.property === path.node) {
                    return;
                }

                // Skip if this would create invalid AST structure
                // Check if this replacement would be valid in the current context
                if (t.isObjectProperty(path.parent) && path.parent.key === path.node) {
                    // Don't replace object property keys
                    return;
                }

                // Add string to map if not already present
                if (!stringMap.has(stringValue)) {
                    stringMap.set(stringValue, stringIndex++);
                }

                // Replace string literal with function call
                const index = stringMap.get(stringValue);
                path.replaceWith(
                    t.callExpression(
                        t.identifier(decoderFunctionName),
                        [t.numericLiteral(index)]
                    )
                );
            }
        });
        
        // If no strings were found, return original code
        if (stringMap.size === 0) {
            return {
                code: code,
                success: true,
                processingTime: Date.now() - startTime,
                originalSize: Buffer.byteLength(code, 'utf8'),
                obfuscatedSize: Buffer.byteLength(code, 'utf8'),
                compressionRatio: 1.0,
                transformsApplied: 'string-compression-no-strings',
                metadata: {
                    engine: 'custom-string-compression',
                    stringsProcessed: 0
                }
            };
        }
        
        // Create compressed string payload
        const stringPayload = Array.from(stringMap.keys()).join(stringDelimiter);
        const compressedString = LZString.compressToUTF16(stringPayload);
        
        // Generate the decoder function and library
        const lzLibrary = createObfuscatedLZStringLibrary();
        const compressedVarName = generateObfuscatedName();
        const decompressedVarName = generateObfuscatedName();
        const indexParamName = generateObfuscatedName();

        const decoderCode = `
var ${decoderFunctionName};
var ${libName} = ${lzLibrary.code};

(function() {
    var ${compressedVarName} = "${compressedString}";
    var ${decompressedVarName} = ${libName}.${lzLibrary.methodName}(${compressedVarName});
    var ${stringArrayName} = ${decompressedVarName}.split("${stringDelimiter}");

    ${decoderFunctionName} = function(${indexParamName}) {
        return ${stringArrayName}[${indexParamName}];
    };
})();
`;
        
        // Generate the final code
        const { code: transformedCode } = babel.transformFromAstSync(ast, code, {
            compact: false,
            minified: false
        });
        
        const finalCode = decoderCode + '\n' + transformedCode;
        
        const processingTime = Date.now() - startTime;
        
        return {
            code: finalCode,
            success: true,
            processingTime: processingTime,
            originalSize: Buffer.byteLength(code, 'utf8'),
            obfuscatedSize: Buffer.byteLength(finalCode, 'utf8'),
            compressionRatio: Buffer.byteLength(finalCode, 'utf8') / Buffer.byteLength(code, 'utf8'),
            transformsApplied: 'custom-string-compression',
            metadata: {
                engine: 'custom-string-compression',
                stringsProcessed: stringMap.size,
                compressionMethod: 'lz-string-utf16',
                decoderFunction: decoderFunctionName
            }
        };
        
    } catch (error) {
        console.error('String compression processing error:', error);
        
        return {
            code: code, // Return original code on failure
            success: false,
            error: error.message,
            processingTime: 0,
            originalSize: Buffer.byteLength(code, 'utf8'),
            obfuscatedSize: Buffer.byteLength(code, 'utf8'),
            compressionRatio: 1.0,
            transformsApplied: 'none',
            metadata: {
                engine: 'custom-string-compression',
                error: error.message
            }
        };
    }
}

module.exports = {
    processStringCompression,
    generateObfuscatedName,
    createObfuscatedLZStringLibrary
};
